# Detailed Implementation Guide: First Click Test Question Type

## Phase 1: Core Setup and Registration

### 1.1 Question Type Registration

**File**: `ko/data/question-types.js`

```javascript
// Add constant
export const FIRST_CLICK_TEST = "24";

// Add to typesSet array
{
  id: FIRST_CLICK_TEST,
  label: "Тест первого клика",
  name: "first-click-test",
}
```

### 1.2 Icon Implementation

**File**: `modules/foquz/views/layouts/_sprite.php`
```html
<symbol id="foquz-icon-question-first-click-test" viewBox="0 0 24 24" fill="none">
  <rect x="2" y="2" width="20" height="16" rx="2" stroke="currentColor" stroke-width="2"/>
  <circle cx="8" cy="10" r="2" fill="currentColor"/>
  <circle cx="16" cy="8" r="1.5" fill="currentColor"/>
  <path d="M12 18l2-2h-4l2 2z" fill="currentColor"/>
</symbol>
```

**File**: `ko/components/ui/icon/types/question-type.less`
```less
.question-icon-set() {
  // ... existing icons
  first-click-test: 24, 24;
}
```

## Phase 2: Question Model Implementation

### 2.1 Main Question Model

**File**: `ko/components/question-form/models/types/first-click-test/index.js`

```javascript
import Question from "../../question";
import { FIRST_CLICK_TEST } from "Data/question-types";
import { FoquzImageLoader } from "Models/file-loader/image-loader";
import { Translator } from "@/utils/translate";

const ValidationTranslator = Translator("validation");

export default class FirstClickTestQuestion extends Question {
  constructor(controller) {
    super(controller);

    this.type = FIRST_CLICK_TEST;

    // Image properties
    this.imageFile = ko.observable(null);
    this.imagePreview = ko.observable("");
    this.clickAreas = ko.observableArray([]);

    // Settings
    this.mobileDisplay = ko.observable("width");
    this.minClicks = ko.observable(1);
    this.maxClicks = ko.observable("");
    this.displayTime = ko.observable("");
    this.buttonText = ko.observable("");
    this.allowClickCancel = ko.observable(false);

    // Options
    this.skipOption = ko.observable(false);
    this.skipText = ko.observable("");
    this.commentOption = ko.observable(false);

    this.initializeImageLoader();
    this.setupValidation();
    this.setupSubscriptions();
  }

  initializeImageLoader() {
    this.imageLoader = new FoquzImageLoader(null, {
      presets: ["image"],
      formats: ["jpg", "jpeg", "png", "gif", "svg"],
      maxSize: 5 * 1024 * 1024, // 5MB
      errors: {
        format: "Можно загружать файлы форматов: jpg, jpeg, png, gif, svg",
        size: "Максимальный размер файла – 5 МБ"
      },
    });

    this.imageLoader.on("select", ({ file }) => {
      this.handleImageSelect(file);
    });

    this.imageLoader.on("preview", ({ url }) => {
      this.imagePreview(url);
    });
  }

  setupValidation() {
    // Image validation
    this.validation.extend({
      required: {
        params: true,
        message: "Изображение обязательно для загрузки",
        onlyIf: () => !this.imageFile()
      }
    });

    // Min clicks validation
    this.minClicks.extend({
      required: {
        params: true,
        message: "Минимальное количество кликов обязательно"
      },
      min: {
        params: 1,
        message: "Минимальное количество кликов должно быть больше 0"
      },
      max: {
        params: 999,
        message: "Максимальное значение: 999"
      }
    });

    // Max clicks validation
    this.maxClicks.extend({
      validation: {
        validator: (value) => {
          if (!value) return true; // Optional field
          const min = parseInt(this.minClicks()) || 1;
          const max = parseInt(value);
          return max >= min;
        },
        message: "Максимальное количество кликов должно быть >= минимального"
      }
    });
  }

  setupSubscriptions() {
    // Clear max clicks if less than min
    this.minClicks.subscribe((newMin) => {
      const max = parseInt(this.maxClicks());
      if (max && max < newMin) {
        this.maxClicks("");
      }
    });
  }

  handleImageSelect(file) {
    this.imageFile(file);
    this.clickAreas([]); // Clear existing areas when new image is selected
  }

  removeImage() {
    this.imageFile(null);
    this.imagePreview("");
    this.clickAreas([]);
    this.imageLoader.remove();
  }

  openFullscreen() {
    // Open image in fullscreen using existing fancybox functionality
    if (this.imagePreview()) {
      $.fancybox.open({
        src: this.imagePreview(),
        type: 'image'
      });
    }
  }

  openClickAreasDialog() {
    this.controller.openSidesheet({
      name: "first-click-areas-sidesheet",
      params: {
        imageUrl: this.imagePreview(),
        areas: this.clickAreas(),
        onSave: (areas) => {
          this.clickAreas(areas);
        }
      }
    });
  }

  updateData(data) {
    super.updateData(data);

    if (data.imageFile) this.imageFile(data.imageFile);
    if (data.imagePreview) this.imagePreview(data.imagePreview);
    if (data.clickAreas) this.clickAreas(data.clickAreas || []);
    if (data.mobileDisplay) this.mobileDisplay(data.mobileDisplay);
    if (data.minClicks !== undefined) this.minClicks(data.minClicks);
    if (data.maxClicks !== undefined) this.maxClicks(data.maxClicks);
    if (data.displayTime !== undefined) this.displayTime(data.displayTime);
    if (data.buttonText !== undefined) this.buttonText(data.buttonText);
    if (data.allowClickCancel !== undefined) this.allowClickCancel(data.allowClickCancel);
    if (data.skipOption !== undefined) this.skipOption(data.skipOption);
    if (data.skipText !== undefined) this.skipText(data.skipText);
    if (data.commentOption !== undefined) this.commentOption(data.commentOption);
  }

  getData() {
    return {
      ...super.getData(),
      imageFile: this.imageFile(),
      imagePreview: this.imagePreview(),
      clickAreas: this.clickAreas().map(area => ({
        name: area.name,
        x: area.x,
        y: area.y,
        width: area.width,
        height: area.height
      })),
      mobileDisplay: this.mobileDisplay(),
      minClicks: parseInt(this.minClicks()) || 1,
      maxClicks: this.maxClicks() ? parseInt(this.maxClicks()) : null,
      displayTime: this.displayTime() ? parseInt(this.displayTime()) : null,
      buttonText: this.buttonText() || "Показать изображение",
      allowClickCancel: this.allowClickCancel(),
      skipOption: this.skipOption(),
      skipText: this.skipText() || "Затрудняюсь ответить",
      commentOption: this.commentOption(),
    };
  }

  validate() {
    const errors = [];

    // Image validation
    if (!this.imageFile()) {
      errors.push("Изображение обязательно для загрузки");
    }

    // Min clicks validation
    const minClicks = parseInt(this.minClicks());
    if (!minClicks || minClicks < 1) {
      errors.push("Минимальное количество кликов должно быть больше 0");
    }

    // Max clicks validation
    const maxClicks = this.maxClicks() ? parseInt(this.maxClicks()) : null;
    if (maxClicks && maxClicks < minClicks) {
      errors.push("Максимальное количество кликов должно быть >= минимального");
    }

    this.validationErrors(errors);
    return errors.length === 0;
  }

  dispose() {
    super.dispose();
    if (this.imageLoader) {
      this.imageLoader.dispose();
    }
  }
}
```

## Phase 3: Template Implementation

### 3.1 Main Template Structure

**File**: `ko/components/question-form/templates/first-click-test.php`

The template will be implemented following the established patterns with proper knockout bindings, form groups, and validation display.

## Phase 4: Click Areas Component

### 4.1 Image Click Areas Component

**File**: `ko/components/image-click-areas/index.js`

```javascript
import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('image-click-areas', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('image-click-areas');
      return new ViewModel(params, element);
    },
  },
  template: html,
});
```

### 4.2 Click Areas Model

**File**: `ko/components/image-click-areas/model.js`

```javascript
import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.imageUrl = params.imageUrl;
    this.areas = ko.observableArray(params.areas || []);
    this.onAreasChange = params.onAreasChange;

    this.selectedArea = ko.observable(null);
    this.isDrawing = ko.observable(false);
    this.drawingArea = ko.observable(null);

    this.canvas = null;
    this.ctx = null;
    this.imageElement = null;

    this.initializeCanvas();
  }

  initializeCanvas() {
    // Canvas initialization for interactive area selection
    // Will be implemented with mouse/touch event handlers
  }

  addArea(coordinates) {
    const area = {
      id: Date.now(),
      name: `Область ${this.areas().length + 1}`,
      x: coordinates.x,
      y: coordinates.y,
      width: coordinates.width,
      height: coordinates.height
    };

    this.areas.push(area);
    this.notifyChange();
  }

  removeArea(area) {
    this.areas.remove(area);
    this.notifyChange();
  }

  updateArea(area, newCoordinates) {
    area.x = newCoordinates.x;
    area.y = newCoordinates.y;
    area.width = newCoordinates.width;
    area.height = newCoordinates.height;
    this.notifyChange();
  }

  notifyChange() {
    if (typeof this.onAreasChange === 'function') {
      this.onAreasChange(this.areas());
    }
  }
}
```

## Phase 5: Formatters Implementation

### 5.1 Client Formatter

**File**: `ko/components/question-form/utils/first-click-test/client-formatter.js`

```javascript
export default function (data, mode) {
  return {
    imageFile: data.imageFile || null,
    imagePreview: data.imagePreview || "",
    clickAreas: data.clickAreas || [],
    mobileDisplay: data.mobileDisplay || "width",
    minClicks: data.minClicks || 1,
    maxClicks: data.maxClicks || "",
    displayTime: data.displayTime || "",
    buttonText: data.buttonText || "",
    allowClickCancel: data.allowClickCancel || false,
    skipOption: data.skip ? true : false,
    skipText: data.skip_text || "",
    commentOption: data.comment || false,
  };
}
```

### 5.2 Server Formatter

**File**: `ko/components/question-form/utils/first-click-test/server-formatter.js`

```javascript
export default function (question, data) {
  question.image_file = data.imageFile;
  question.click_areas = JSON.stringify(data.clickAreas);
  question.mobile_display = data.mobileDisplay;
  question.min_clicks = data.minClicks;
  question.max_clicks = data.maxClicks;
  question.display_time = data.displayTime;
  question.button_text = data.buttonText;
  question.allow_click_cancel = data.allowClickCancel ? 1 : 0;
  question.skip = data.skipOption ? 1 : 0;
  question.skip_text = data.skipText;
  question.comment = data.commentOption ? 1 : 0;

  return question;
}
```

## Phase 6: Registration and Integration

### 6.1 Model Registration

**File**: `ko/components/question-form/index.js`

```javascript
import FirstClickTestQuestion from "./models/types/first-click-test";

const QuestionModels = {
  // ... existing models
  [types.FIRST_CLICK_TEST]: FirstClickTestQuestion,
};
```

### 6.2 Template Include

**File**: `ko/components/question-form/_components.php`

```php
<?= $this->render('templates/first-click-test.php'); ?>
```

### 6.3 Formatters Registration

**File**: `ko/components/question-form/utils/formatters.js`

```javascript
import firstClickTestFormatter from "./first-click-test";

export const formatters = {
  // ... existing formatters
  [questionTypes.FIRST_CLICK_TEST]: firstClickTestFormatter,
};
```

## Phase 7: Sidesheet Implementation

### 7.1 Sidesheet Component

**File**: `ko/dialogs/first-click-areas-sidesheet/index.js`

```javascript
import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('first-click-areas-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('first-click-areas-sidesheet');
      return new ViewModel(params, element);
    },
  },
  template: html,
});
```

### 7.2 Sidesheet Model

**File**: `ko/dialogs/first-click-areas-sidesheet/model.js`

```javascript
import { DialogWrapper } from "Dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.imageUrl = params.imageUrl;
    this.areas = ko.observableArray([...params.areas] || []);
    this.onSave = params.onSave;

    this.selectedArea = ko.observable(null);
    this.editingArea = ko.observable(null);

    // Form for area editing
    this.areaForm = {
      name: ko.observable(""),
      x: ko.observable(0),
      y: ko.observable(0),
      width: ko.observable(100),
      height: ko.observable(100)
    };
  }

  addNewArea() {
    const newArea = {
      id: Date.now(),
      name: `Область ${this.areas().length + 1}`,
      x: 50,
      y: 50,
      width: 100,
      height: 100
    };

    this.areas.push(newArea);
    this.editArea(newArea);
  }

  editArea(area) {
    this.editingArea(area);
    this.areaForm.name(area.name);
    this.areaForm.x(area.x);
    this.areaForm.y(area.y);
    this.areaForm.width(area.width);
    this.areaForm.height(area.height);
  }

  saveAreaEdit() {
    const area = this.editingArea();
    if (area) {
      area.name = this.areaForm.name();
      area.x = parseInt(this.areaForm.x());
      area.y = parseInt(this.areaForm.y());
      area.width = parseInt(this.areaForm.width());
      area.height = parseInt(this.areaForm.height());

      this.areas.valueHasMutated();
      this.editingArea(null);
    }
  }

  cancelAreaEdit() {
    this.editingArea(null);
  }

  removeArea(area) {
    this.areas.remove(area);
    if (this.editingArea() === area) {
      this.editingArea(null);
    }
  }

  saveAreas() {
    if (typeof this.onSave === 'function') {
      this.onSave([...this.areas()]);
    }
    this.hide();
  }

  cancelChanges() {
    this.hide();
  }
}
```

## Phase 8: Testing Strategy

### 8.1 Unit Tests

1. **Question Model Tests**:
   - Test data initialization and updates
   - Test validation rules
   - Test image upload handling
   - Test click areas management

2. **Component Tests**:
   - Test template rendering
   - Test user interactions
   - Test data binding

3. **Formatter Tests**:
   - Test data transformation between client and server
   - Test edge cases and null values

### 8.2 Integration Tests

1. **Question Type Selection**: Verify new type appears in dropdown
2. **Form Submission**: Test complete form data flow
3. **Sidesheet Integration**: Test sidesheet opening and data passing
4. **Image Upload**: Test file upload and preview functionality

### 8.3 User Acceptance Tests

1. **UI/UX Compliance**: Match provided screenshots and requirements
2. **Validation Behavior**: Test all validation rules
3. **Cross-browser Testing**: Ensure compatibility
4. **Mobile Responsiveness**: Test responsive behavior

This implementation guide provides the detailed structure and code patterns needed to implement the First Click Test question type following the established codebase conventions.
