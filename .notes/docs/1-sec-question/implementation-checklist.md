# Implementation Checklist: First Click Test Question Type

## Phase 1: Core Setup ✅

### Question Type Registration
- [ ] Add `FIRST_CLICK_TEST = "24"` constant to `ko/data/question-types.js`
- [ ] Add type entry to `typesSet` array with label "Тест первого клика" and name "first-click-test"
- [ ] Verify type appears in question type dropdown

### Icon Implementation
- [ ] Create SVG symbol in `modules/foquz/views/layouts/_sprite.php` with id `foquz-icon-question-first-click-test`
- [ ] Add icon dimensions to `ko/components/ui/icon/types/question-type.less`
- [ ] Test icon display in question type selector

## Phase 2: Question Model ✅

### Main Model File
- [ ] Create `ko/components/question-form/models/types/first-click-test/index.js`
- [ ] Implement `FirstClickTestQuestion` class extending base `Question`
- [ ] Add all required observables (imageFile, clickAreas, settings, etc.)
- [ ] Implement validation logic for required fields
- [ ] Add image loader initialization
- [ ] Implement `updateData()` and `getData()` methods

### Gallery Integration (Updated based on API)
- [ ] Initialize `GalleryController` with correct file formats (jpg, jpeg, png, gif, svg)
- [ ] Set 5MB file size limit and maxFiles: 1
- [ ] Handle `enableGallery` flag and `gallery` array
- [ ] Integrate with existing gallery components
- [ ] Add fullscreen image viewing using existing gallery functionality

## Phase 3: Template Implementation ✅

### Main Template (Updated for Gallery Integration)
- [ ] Create `ko/components/question-form/templates/first-click-test.php`
- [ ] Implement gallery upload area using existing gallery components
- [ ] Add gallery preview with action buttons
- [ ] Create mobile display settings dropdown (0=width, 1=height)
- [ ] Add min/max click count inputs with validation
- [ ] Implement display time input field
- [ ] Add button text input with character counter
- [ ] Create options section (cancel clicks, skip, comment) using existing components

### Template Registration
- [ ] Add template include to `ko/components/question-form/_components.php`
- [ ] Test template rendering with knockout bindings

## Phase 4: Click Areas Component ✅

### Component Structure
- [ ] Create `ko/components/image-click-areas/` directory
- [ ] Implement component registration in `index.js`
- [ ] Create component model with area management logic
- [ ] Design template for interactive area selection
- [ ] Add component styling

### Integration
- [ ] Test component integration with main question form
- [ ] Verify area data persistence

## Phase 5: Sidesheet Implementation ✅

### Sidesheet Component
- [ ] Create `ko/dialogs/first-click-areas-sidesheet/` directory
- [ ] Implement sidesheet registration
- [ ] Create sidesheet model extending `DialogWrapper`
- [ ] Design sidesheet template with image and areas list
- [ ] Add area editing functionality

### Sidesheet Features
- [ ] Image display with area overlays
- [ ] Area creation and editing forms
- [ ] Area removal functionality
- [ ] Save/cancel actions
- [ ] Validation for area properties

## Phase 6: Formatters Implementation ✅

### Client Formatter
- [ ] Create `ko/components/question-form/utils/first-click-test/client-formatter.js`
- [ ] Implement data transformation from server to client format
- [ ] Handle default values and optional fields

### Server Formatter
- [ ] Create `server-formatter.js`
- [ ] Transform client data to server format
- [ ] Handle JSON serialization for complex data

### Preview Formatter
- [ ] Create `preview-formatter.js`
- [ ] Format data for preview display
- [ ] Include summary information

### CPoint Server Formatter
- [ ] Create `cpoint-server-formatter.js`
- [ ] Handle contact point specific formatting

### Formatter Registration
- [ ] Create `index.js` in formatters directory
- [ ] Export all formatters
- [ ] Register in main formatters registry

## Phase 7: System Integration ✅

### Model Registration
- [ ] Add `FirstClickTestQuestion` to `QuestionModels` in `ko/components/question-form/index.js`
- [ ] Test question model instantiation

### Formatters Registration
- [ ] Add formatters to `ko/components/question-form/utils/formatters.js`
- [ ] Test data transformation pipeline

### Dependencies
- [ ] Ensure all required components are imported
- [ ] Verify sidesheet registration
- [ ] Test component dependencies

## Phase 8: Styling ✅

### Main Question Styles
- [ ] Create styles for image upload area
- [ ] Style image preview container
- [ ] Add responsive design for mobile
- [ ] Style form sections and help text

### Sidesheet Styles
- [ ] Style image areas preview
- [ ] Design area overlay appearance
- [ ] Style areas management interface
- [ ] Add hover and selection states

### Integration Testing
- [ ] Test styles across different browsers
- [ ] Verify responsive behavior
- [ ] Check accessibility compliance

## Phase 9: Validation & Testing ✅

### Unit Tests
- [ ] Test question model initialization
- [ ] Test validation rules
- [ ] Test data transformation
- [ ] Test component interactions

### Integration Tests
- [ ] Test question type selection
- [ ] Test form submission flow
- [ ] Test sidesheet integration
- [ ] Test image upload functionality

### User Acceptance Tests
- [ ] Verify UI matches requirements
- [ ] Test all user interactions
- [ ] Validate error handling
- [ ] Test cross-browser compatibility

## Phase 10: Documentation & Deployment ✅

### Code Documentation
- [ ] Add JSDoc comments to all methods
- [ ] Document component interfaces
- [ ] Create usage examples

### User Documentation
- [ ] Create user guide for question type
- [ ] Document configuration options
- [ ] Add troubleshooting guide

### Deployment Preparation
- [ ] Verify all files are included
- [ ] Test in staging environment
- [ ] Prepare rollback plan
- [ ] Schedule deployment

## Critical Requirements Verification

### Image Upload Requirements
- [ ] ✅ Supports jpg, jpeg, png, gif, svg formats
- [ ] ✅ 5MB file size limit enforced
- [ ] ✅ Drag & drop functionality
- [ ] ✅ Image preview with fullscreen view
- [ ] ✅ Delete functionality

### Click Areas Requirements
- [ ] ✅ Sidesheet for area management
- [ ] ✅ Area name, coordinates, dimensions
- [ ] ✅ Visual area overlays on image
- [ ] ✅ Area editing and removal

### Form Fields Requirements
- [ ] ✅ Mobile display dropdown (width/height)
- [ ] ✅ Min clicks (required, 1-999)
- [ ] ✅ Max clicks (optional, >= min)
- [ ] ✅ Display time (optional, seconds)
- [ ] ✅ Button text (30 char limit, default "Показать изображение")
- [ ] ✅ Allow click cancellation checkbox
- [ ] ✅ Skip option with custom text
- [ ] ✅ Comment option

### Validation Requirements
- [ ] ✅ Image upload is required
- [ ] ✅ Min clicks validation
- [ ] ✅ Max clicks >= min clicks
- [ ] ✅ Character limits enforced
- [ ] ✅ Proper error messages

## Post-Implementation Tasks

### Performance Optimization
- [ ] Optimize image loading
- [ ] Minimize bundle size impact
- [ ] Test with large images

### Monitoring & Analytics
- [ ] Add usage tracking
- [ ] Monitor error rates
- [ ] Track performance metrics

### Future Enhancements
- [ ] Consider drag-to-create areas
- [ ] Add area templates
- [ ] Implement area grouping
- [ ] Add click heatmap preview

This checklist ensures comprehensive implementation of the First Click Test question type according to all specified requirements and established codebase patterns.
